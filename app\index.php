<?php
/**
 * App Directory Index - Indonesian PDF Letter Generator
 * 
 * This file redirects users to the appropriate page
 * since the /app/ directory contains application structure files
 * and should not be accessed directly.
 */

// Prevent direct access to app directory
header('HTTP/1.1 403 Forbidden');

// Get the base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$baseUrl = $protocol . '://' . $host;

// Determine the correct redirect path
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = dirname(dirname($requestUri)); // Go up two levels from /app/

// If we're in a subdirectory like /surat/app/, redirect to /surat/
if (strpos($basePath, '/surat') !== false) {
    $redirectUrl = $baseUrl . '/surat/home';
} else {
    $redirectUrl = $baseUrl . '/home.php';
}

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>403 - Akses Ditolak | Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #1E40AF;
            --primary-navy: #1E293B;
            --dark-navy: #0F172A;
            --indonesian-red: #B91C1C;
            --golden-yellow: #D97706;
            --slate-gray: #334155;
            --text-primary: #F8FAFC;
            --text-secondary: #E2E8F0;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #0F172A 0%, #1E293B 30%, #334155 70%, #475569 100%);
        }

        .hero-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='3'/%3E%3Ccircle cx='10' cy='10' r='2'/%3E%3Ccircle cx='50' cy='50' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .glass-card {
            background: rgba(248, 250, 252, 0.95);
            backdrop-filter: blur(16px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #1E40AF, #1E293B);
            color: white;
            transition: all 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1D4ED8, #0F172A);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(30, 64, 175, 0.4);
        }

        .error-icon {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="gradient-bg hero-pattern min-h-screen flex items-center justify-center p-4">
    <div class="glass-card rounded-2xl p-8 max-w-md w-full text-center">
        <!-- Error Icon -->
        <div class="error-icon mb-6">
            <i class="fas fa-ban text-6xl" style="color: var(--indonesian-red);"></i>
        </div>

        <!-- Error Title -->
        <h1 class="text-3xl font-bold mb-4" style="color: var(--slate-gray);">
            403 - Akses Ditolak
        </h1>

        <!-- Error Message -->
        <p class="text-lg mb-6 leading-relaxed" style="color: var(--slate-gray);">
            Maaf, Anda tidak memiliki izin untuk mengakses direktori aplikasi ini secara langsung.
        </p>

        <!-- Additional Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-center justify-center mb-2">
                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                <span class="font-semibold text-blue-800">Informasi</span>
            </div>
            <p class="text-sm text-blue-700">
                Direktori <code>/app/</code> berisi file struktur aplikasi yang tidak dapat diakses langsung untuk keamanan.
            </p>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
            <a href="<?php echo $redirectUrl; ?>" 
               class="btn-primary w-full py-3 px-6 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                <i class="fas fa-home mr-3"></i>Kembali ke Beranda
            </a>
            
            <div class="text-sm" style="color: var(--slate-gray);">
                <p>Atau kunjungi:</p>
                <div class="mt-2 space-y-1">
                    <a href="<?php echo $baseUrl; ?>/surat/login" class="text-blue-600 hover:text-blue-800 transition-colors">
                        <i class="fas fa-sign-in-alt mr-1"></i>Halaman Login
                    </a>
                    <br>
                    <a href="<?php echo $baseUrl; ?>/surat/register" class="text-blue-600 hover:text-blue-800 transition-colors">
                        <i class="fas fa-user-plus mr-1"></i>Halaman Registrasi
                    </a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <p class="text-xs" style="color: var(--slate-gray);">
                Indonesian PDF Letter Generator &copy; 2025
            </p>
        </div>
    </div>

    <script>
        // Auto redirect after 10 seconds
        setTimeout(function() {
            window.location.href = '<?php echo $redirectUrl; ?>';
        }, 10000);

        // Show countdown
        let countdown = 10;
        const countdownElement = document.createElement('div');
        countdownElement.className = 'mt-4 text-sm';
        countdownElement.style.color = 'var(--slate-gray)';
        countdownElement.innerHTML = `<i class="fas fa-clock mr-1"></i>Redirect otomatis dalam <span id="countdown">${countdown}</span> detik`;
        
        document.querySelector('.glass-card').appendChild(countdownElement);
        
        const timer = setInterval(function() {
            countdown--;
            document.getElementById('countdown').textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
            }
        }, 1000);
    </script>
</body>
</html>
