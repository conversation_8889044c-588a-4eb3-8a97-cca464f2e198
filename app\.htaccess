# Indonesian PDF Letter Generator - App Directory Protection
# Prevent direct access to application structure files

# Deny access to all files in this directory
Order Deny,Allow
Deny from all

# Allow access only to index.php for proper error handling
<Files "index.php">
    Order Allow,<PERSON>y
    Allow from all
</Files>

# Prevent directory browsing
Options -Indexes

# Hide sensitive files
<FilesMatch "\.(php|inc|conf|sql|log)$">
    Order Allow,<PERSON>y
    <PERSON> from all
</FilesMatch>

# Allow only index.php
<FilesMatch "^index\.php$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Redirect any direct access to app directory to home
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [L]
