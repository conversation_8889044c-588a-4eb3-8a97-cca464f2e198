<?php

/**
 * Main entry point for Indonesian PDF Letter Generator
 * Redirects to the home page for better user experience
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get the current URL to determine redirect path
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$requestUri = $_SERVER['REQUEST_URI'];

// Determine if we're in a subdirectory
$basePath = dirname($requestUri);
$isSubdirectory = $basePath !== '/' && $basePath !== '';

// Prevent redirect loops
if (!isset($_SESSION['redirect_count'])) {
    $_SESSION['redirect_count'] = 0;
}

// Reset redirect count if it's been more than 30 seconds
if (isset($_SESSION['last_redirect_time']) && (time() - $_SESSION['last_redirect_time']) > 30) {
    $_SESSION['redirect_count'] = 0;
}

$_SESSION['redirect_count']++;
$_SESSION['last_redirect_time'] = time();

if ($_SESSION['redirect_count'] > 3) {
    // Too many redirects, show error page
    unset($_SESSION['redirect_count']);
    unset($_SESSION['last_redirect_time']);

    // Show a simple error page instead of dying
?>
    <!DOCTYPE html>
    <html lang="id">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Error - Indonesian PDF Letter Generator</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>

    <body class="bg-gray-100 min-h-screen flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
            <i class="fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"></i>
            <h1 class="text-2xl font-bold text-gray-800 mb-4">Terjadi Masalah</h1>
            <p class="text-gray-600 mb-6">Redirect loop terdeteksi. Silakan hapus cache browser dan cookies, lalu coba lagi.</p>
            <a href="home.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-home mr-2"></i>Kembali ke Beranda
            </a>
        </div>
    </body>

    </html>
<?php
    exit;
}

// Determine the correct redirect URL
if ($isSubdirectory) {
    // We're in a subdirectory like /surat/
    $redirectUrl = $basePath . '/home';
} else {
    // We're in the root directory
    $redirectUrl = '/home.php';
}

// Clear redirect count on successful redirect
unset($_SESSION['redirect_count']);
unset($_SESSION['last_redirect_time']);

// Redirect to home page
header('Location: ' . $redirectUrl);
exit;
